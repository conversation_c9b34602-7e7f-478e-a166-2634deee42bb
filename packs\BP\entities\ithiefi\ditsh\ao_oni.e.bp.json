{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:ao_oni", "is_spawnable": true, "is_summonable": true}, "component_groups": {"music_playing": {"minecraft:timer": {"looping": true, "time": 22.83, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}}, "events": {"ditsh:on_death": {"queue_command": {"command": "stopsound @a mob.ditsh.ao_oni.chase"}}, "ditsh:start_chase_music": {"add": {"component_groups": ["music_playing"]}, "queue_command": {"command": ["playsound mob.ditsh.ao_oni.chase @a[r=32] ~ ~ ~ 1.0 1.0", ""]}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["music_playing"]}, "queue_command": {"command": "stopsound @a[r=32] mob.ditsh.ao_oni.chase"}}, "ditsh:maintain_chase_music": {"queue_command": {"command": "playsound mob.ditsh.ao_oni.chase @a[r=32] ~ ~ ~ 1.0 1.0"}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "ao_oni", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.6, "height": 2.4}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.2}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 10}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.15}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "villager"}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_death": {"event": "ditsh:on_death", "target": "self"}, "minecraft:entity_sensor": {"subsensors": [{"event": "ditsh:start_chase_music", "cooldown": 1.0, "range": [24, 12], "minimum_count": 1, "event_filters": {"test": "has_target", "subject": "self", "value": true}}, {"event": "ditsh:stop_chase_music", "cooldown": 1.0, "range": [24, 12], "minimum_count": 0, "maximum_count": 0, "event_filters": {"test": "has_target", "subject": "self", "value": false}}]}}}}