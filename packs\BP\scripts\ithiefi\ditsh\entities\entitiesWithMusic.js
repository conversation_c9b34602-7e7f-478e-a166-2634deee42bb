import { system } from "@minecraft/server";
export const entitiesWithMusic = new Map([["ditsh:ao_oni", "mob.ditsh.ao_oni.chase"]]);
export function playMusicForEntity(entity, music) {
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    for (const player of nearbyPlayers) {
        if (!isPlayerPlayingMusic(player, entity.typeId)) {
            player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
            player.setDynamicProperty(`${entity.typeId}_music`, true);
        }
    }
    return;
}
export function stopMusicForEntity(entity, music) {
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    for (const player of nearbyPlayers) {
        if (isPlayerPlayingMusic(player, entity.typeId)) {
            player.runCommand(`stopsound @s ${music}`);
            player.setDynamicProperty(`${entity.typeId}_music`, false);
        }
    }
    return;
}
export async function continueMusicForEntity(entity, music) {
    const playMusic = entity.getProperty("ditsh:playing_music");
    if (playMusic) {
        await system.waitTicks(140);
        const nearbyPlayers = getNearbyPlayers(entity, 256);
        for (const player of nearbyPlayers) {
            if (!isPlayerPlayingMusic(player, entity.typeId)) {
                player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
                player.setDynamicProperty(`${entity.typeId}_music`, true);
            }
        }
    }
    return;
}
export function resetPlayerMusic(player) {
    for (const [entityTypeId, music] of entitiesWithMusic) {
        if (isPlayerPlayingMusic(player, entityTypeId)) {
            player.runCommand(`stopsound @s ${music}`);
            player.setDynamicProperty(`${entityTypeId}_music`, false);
        }
    }
    return;
}
function getNearbyPlayers(entity, range) {
    const players = entity.dimension.getPlayers({
        location: entity.location,
        maxDistance: range
    });
    return players;
}
function isPlayerPlayingMusic(player, entityTypeId) {
    const playingMusic = player.getDynamicProperty(`${entityTypeId}_music`);
    return playingMusic;
}
